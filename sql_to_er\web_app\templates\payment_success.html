<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付成功 - 学术工具集</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c3e50;
        }

        .success-container {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
            animation: bounce 1s ease-out 0.3s both;
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -15px, 0);
            }
            70% {
                transform: translate3d(0, -7px, 0);
            }
            90% {
                transform: translate3d(0, -2px, 0);
            }
        }

        .success-icon i {
            font-size: 40px;
            color: white;
        }

        .success-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .success-message {
            font-size: 16px;
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 40px;
        }

        .success-amount {
            font-size: 24px;
            font-weight: 600;
            color: #10b981;
            background: #ecfdf5;
            padding: 12px 24px;
            border-radius: 12px;
            margin-bottom: 40px;
            display: inline-block;
        }

        .buttons-group {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 14px 28px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: white;
            color: #374151;
            border: 2px solid #e5e7eb;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            transform: translateY(-2px);
            color: #374151;
            text-decoration: none;
        }

        .countdown {
            margin-top: 30px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            color: #64748b;
            font-size: 14px;
        }

        .countdown-timer {
            font-weight: 600;
            color: #3b82f6;
        }

        .features {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            text-align: left;
        }

        .features-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            text-align: center;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            color: #6b7280;
        }

        .feature-item i {
            color: #10b981;
            width: 16px;
        }

        @media (max-width: 480px) {
            .success-container {
                padding: 40px 20px;
            }
            
            .buttons-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>
        
        <h1 class="success-title">支付成功！</h1>
        
        <p class="success-message">
            恭喜您，充值已成功完成！<br>
            您的账户余额已实时更新，可以立即使用各项服务。
        </p>
        
        <div class="success-amount">
            <i class="fas fa-coins"></i> 充值金额已到账
        </div>
        
        <div class="buttons-group">
            <a href="/profile" class="btn btn-primary">
                <i class="fas fa-user"></i>
                查看余额
            </a>
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-home"></i>
                返回首页
            </a>
        </div>
        
        <div class="features">
            <div class="features-title">现在您可以使用以下服务</div>
            <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                SQL转ER图工具
            </div>
            <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                AI论文生成器
            </div>
            <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                测试用例生成器
            </div>
            <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                论文答辩问题生成
            </div>
        </div>
        
        <div class="countdown">
            <i class="fas fa-clock"></i>
            <span id="countdown-text">5秒后自动跳转到个人中心...</span>
        </div>
    </div>

    <script>
        let countdown = 5;
        const countdownEl = document.getElementById('countdown-text');
        
        const timer = setInterval(() => {
            countdown--;
            countdownEl.innerHTML = `<span class="countdown-timer">${countdown}</span>秒后自动跳转到个人中心...`;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '/profile';
            }
        }, 1000);
        
        // 如果用户点击了其他链接，清除倒计时
        document.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', () => {
                clearInterval(timer);
            });
        });
        
        // 添加页面可见性检测，防止后台运行时继续倒计时
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                clearInterval(timer);
            }
        });
    </script>
</body>
</html>