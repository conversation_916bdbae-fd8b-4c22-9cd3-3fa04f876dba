<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI论文生成器 - 智能学术助手</title>
    
    <!-- Quill.js CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/quill/1.3.7/quill.snow.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- docx.js and mammoth.js for Word document processing -->
    <script src="https://unpkg.com/docx@8.2.2/build/index.js"></script>
    <script src="https://unpkg.com/mammoth@1.6.0/mammoth.browser.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #5e72e4;
            --primary-dark: #4c63d2;
            --primary-light: #8896ff;
            --secondary-color: #11cdef;
            --success-color: #2dce89;
            --danger-color: #f5365c;
            --warning-color: #fb6340;
            --info-color: #11cdef;
            --dark: #32325d;
            --gray: #8898aa;
            --gray-light: #f4f5f7;
            --white: #ffffff;
            --shadow-sm: 0 0 .5rem rgba(0,0,0,.075);
            --shadow: 0 0 2rem 0 rgba(136,152,170,.15);
            --shadow-lg: 0 0 3rem rgba(0,0,0,.175);
        }

        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #f8f9fe;
            min-height: 100vh;
            color: var(--dark);
            line-height: 1.6;
            font-size: 15px;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            z-index: -2;
        }

        .bg-decoration::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
        }

        .bg-decoration::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        /* 添加浮动装饰元素 */
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-circle:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-circle:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-circle:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .floating-circle:nth-child(4) {
            width: 100px;
            height: 100px;
            bottom: 20%;
            right: 10%;
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .bg-decoration {
                height: 100vh;
            }

            .floating-circle {
                display: none;
            }

            .container {
                padding: 20px 15px;
            }

            .card:hover {
                transform: translateY(-4px) scale(1.01);
            }

            .header h1 {
                font-size: 2rem;
            }

            .header p {
                font-size: 1rem;
            }

            .back-home-btn {
                top: 15px;
                left: 15px;
                padding: 10px 16px;
                font-size: 0.8rem;
            }

            .draft-notification-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .draft-notification-actions {
                justify-content: center;
            }

            body.draft-notification-visible {
                padding-top: 90px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5rem;
            }

            .card {
                border-radius: 15px;
                margin-bottom: 20px;
            }

            .back-home-btn {
                top: 10px;
                left: 10px;
                padding: 8px 12px;
                font-size: 0.75rem;
                gap: 6px;
            }

            .back-home-btn i {
                font-size: 0.9rem;
            }

            .draft-notification {
                padding: 12px 15px;
            }

            .draft-notification-text {
                font-size: 0.85rem;
            }

            .draft-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            body.draft-notification-visible {
                padding-top: 100px;
            }
        }

        /* 头部标题 */
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            filter: blur(40px);
        }

        /* 返回首页按钮 */
        .back-home-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-home-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: white;
            text-decoration: none;
        }

        .back-home-btn i {
            font-size: 1rem;
        }

        /* 智能目录模式下隐藏特殊要求和普通生成按钮 */
        .intelligent-mode .requirements-section,
        .intelligent-mode .normal-generate-btn {
            display: none !important;
        }

        /* 草稿恢复提示条 */
        .draft-notification {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            z-index: 1000;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .draft-notification.show {
            transform: translateY(0);
        }

        .draft-notification-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
        }

        .draft-notification-text {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 0.95rem;
        }

        .draft-notification-text i {
            font-size: 1.2rem;
            color: #ffd89b;
        }

        .draft-notification-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .draft-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .draft-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .draft-btn.primary {
            background: rgba(255, 216, 155, 0.3);
            border-color: rgba(255, 216, 155, 0.5);
        }

        .draft-btn.primary:hover {
            background: rgba(255, 216, 155, 0.4);
        }

        .draft-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .draft-close:hover {
            opacity: 1;
        }

        /* 当显示草稿提示时，调整页面内容位置 */
        body.draft-notification-visible {
            padding-top: 70px;
        }

        .header h1 {
            color: white;
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 12px;
            letter-spacing: -1px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header h1 i {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .header p {
            color: rgba(255,255,255,0.95);
            font-size: 1.15rem;
            font-weight: 400;
            opacity: 0.9;
        }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 30px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.4);
        }

        .card-header {
            background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fe 100%);
            padding: 25px 35px;
            border-bottom: 1px solid rgba(0,0,0,0.06);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-header h2 {
            font-size: 1.35rem;
            font-weight: 600;
            color: var(--dark);
            margin: 0;
            letter-spacing: -0.5px;
        }

        .card-header i {
            font-size: 1.3rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .card-content {
            padding: 35px;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #525f7f;
            margin-bottom: 10px;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .form-label .required {
            color: var(--danger-color);
            margin-left: 2px;
        }

        .form-input, .form-select, .form-textarea {
            padding: 12px 18px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fe;
            color: var(--dark);
            font-weight: 500;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 4px rgba(94, 114, 228, 0.1);
            transform: translateY(-1px);
        }

        .form-textarea {
            resize: vertical;
            min-height: 110px;
            font-family: inherit;
            line-height: 1.5;
        }

        /* 标题级别选择 */
        .title-level-section {
            margin-bottom: 30px;
        }

        .title-level-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .level-option {
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8f9fe 0%, #ffffff 100%);
            position: relative;
            overflow: hidden;
        }

        .level-option::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(94, 114, 228, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .level-option:hover::before {
            opacity: 1;
            top: -100%;
            right: -100%;
        }

        .level-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(94, 114, 228, 0.1);
        }

        .level-option.active {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #5e72e4 0%, #825ee4 100%);
            color: white;
            box-shadow: 0 10px 20px rgba(94, 114, 228, 0.3);
        }

        .level-option.active .level-title,
        .level-option.active .level-desc {
            color: white;
        }

        .level-title {
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--dark);
            font-size: 1.1rem;
        }

        .level-desc {
            font-size: 0.9rem;
            color: var(--gray);
            line-height: 1.5;
        }

        /* 生成按钮 */
        .generate-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            padding: 16px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
            margin: 40px auto 0;
            min-width: 220px;
            box-shadow: 0 4px 15px rgba(94, 114, 228, 0.4);
            position: relative;
            overflow: hidden;
        }

        .generate-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }

        .generate-btn:hover::before {
            left: 100%;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(94, 114, 228, 0.5);
        }

        .generate-btn:active {
            transform: translateY(0);
        }

        .generate-btn:disabled {
            background: linear-gradient(135deg, #adb5bd 0%, #868e96 100%);
            cursor: not-allowed;
            box-shadow: none;
        }

        /* 进度条 */
        .progress-section {
            display: none;
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow);
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .progress-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(180deg, var(--primary-color), var(--primary-light));
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .progress-title {
            font-weight: 700;
            color: var(--dark);
            font-size: 1.2rem;
        }

        .progress-percent {
            font-weight: 700;
            font-size: 1.3rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 20px;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            border-radius: 6px;
            transition: width 0.5s ease;
            width: 0%;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(94, 114, 228, 0.3);
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        .progress-message {
            color: var(--gray);
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .progress-message::before {
            content: '';
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(0.8);
            }
        }

        /* 智能目录生成样式 */
        .intelligent-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 2px solid #e1bee7;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .intelligent-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(156, 39, 176, 0.1) 0%, transparent 70%);
            border-radius: 50%;
        }

        .intelligent-actions {
            text-align: center;
            margin: 20px 0;
        }

        .outline-btn {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .outline-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(156, 39, 176, 0.4);
        }

        .outline-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .analysis-result {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .analysis-header h4 {
            margin: 0 0 15px 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .analysis-content {
            margin: 15px 0;
        }

        .analysis-info {
            background: #f8f9fa;
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
        }

        .outline-preview {
            background: #fafafa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .outline-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .outline-item:last-child {
            border-bottom: none;
        }

        .outline-item.level-1 {
            font-weight: bold;
            color: var(--dark);
        }

        .outline-item.level-2 {
            margin-left: 20px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .outline-item.level-3 {
            margin-left: 40px;
            color: var(--gray);
        }

        .outline-words {
            color: #666;
            font-size: 0.9em;
            margin-left: auto;
        }

        .analysis-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .confirm-btn, .regenerate-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .confirm-btn {
            background: var(--success-color);
            color: white;
        }

        .confirm-btn:hover {
            background: #27ae60;
            transform: translateY(-1px);
        }

        .regenerate-btn {
            background: var(--warning-color);
            color: white;
        }

        .regenerate-btn:hover {
            background: #e55a3c;
            transform: translateY(-1px);
        }

        /* 编辑器区域 */
        .editor-section {
            position: relative;
            overflow: hidden;
        }

        .editor-header {
            background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fe 100%);
            padding: 25px 35px;
            border-bottom: 1px solid rgba(0,0,0,0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .editor-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.35rem;
            font-weight: 600;
            color: var(--dark);
        }

        .editor-title i {
            font-size: 1.3rem;
            background: linear-gradient(135deg, var(--secondary-color), #1f9bcf);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .editor-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            padding: 10px 20px;
            border: 2px solid transparent;
            background: white;
            color: var(--gray);
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .action-btn:hover {
            color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(94, 114, 228, 0.15);
        }

        .action-btn i {
            font-size: 0.9rem;
        }

        /* 增强工具栏样式 */
        .enhanced-toolbar {
            background: #f8fafc;
            padding: 15px 30px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .toolbar-group label {
            font-size: 0.85rem;
            font-weight: 600;
            color: #64748b;
            margin: 0;
        }

        .toolbar-select {
            border: none;
            background: transparent;
            font-size: 0.9rem;
            color: #1e293b;
            cursor: pointer;
            min-width: 80px;
        }

        .toolbar-input {
            border: none;
            background: transparent;
            width: 60px;
            font-size: 0.9rem;
            color: #1e293b;
            text-align: center;
        }

        .toolbar-group .action-btn {
            margin: 0;
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        /* Quill编辑器样式优化 */
        .editor-container {
            height: 650px;
            background: white;
        }

        .ql-toolbar {
            border: none !important;
            border-bottom: 2px solid #f0f3f5 !important;
            padding: 15px 20px !important;
            background: #fafbfc;
        }

        .ql-container {
            border: none !important;
            font-size: 16px !important;
            line-height: 1.8 !important;
        }

        .ql-editor {
            padding: 40px 50px !important;
            color: #2c3e50;
            max-width: 900px;
            margin: 0 auto;
        }

        .ql-editor h1 {
            font-size: 2.2rem !important;
            color: #1a202c !important;
            margin-bottom: 30px !important;
            text-align: center !important;
            font-weight: 700 !important;
            line-height: 1.3 !important;
        }

        .ql-editor h2 {
            font-size: 1.6rem !important;
            color: #2d3748 !important;
            margin-top: 35px !important;
            margin-bottom: 20px !important;
            font-weight: 600 !important;
            padding-bottom: 10px !important;
            border-bottom: 3px solid #e2e8f0 !important;
            position: relative !important;
        }

        .ql-editor h3 {
            font-size: 1.3rem !important;
            color: #4a5568 !important;
            margin-top: 28px !important;
            margin-bottom: 15px !important;
            font-weight: 600 !important;
        }

        .ql-editor p {
            margin-bottom: 18px !important;
            text-indent: 2em !important;
            line-height: 1.8 !important;
            color: #4a5568 !important;
        }

        /* 成功提示 */
        .success-message {
            background: linear-gradient(135deg, var(--success-color), #20b869);
            color: white;
            padding: 18px 30px;
            border-radius: 12px;
            margin-bottom: 25px;
            display: none;
            align-items: center;
            gap: 15px;
            box-shadow: 0 4px 15px rgba(45, 206, 137, 0.3);
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .success-message::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: slideIn 0.6s ease forwards;
        }

        @keyframes slideIn {
            to {
                left: 100%;
            }
        }

        .success-message i {
            font-size: 1.4rem;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 0.8s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 加载遮罩 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-overlay .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #667eea;
        }

        /* 提示信息 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.error {
            background: #ef4444;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body::before {
                height: 300px;
            }

            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .card-content {
                padding: 25px 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .title-level-options {
                grid-template-columns: 1fr;
            }

            .editor-container {
                height: 500px;
            }

            .editor-actions {
                flex-wrap: wrap;
            }

            .action-btn {
                padding: 8px 16px;
                font-size: 0.85rem;
            }

            .ql-editor {
                padding: 25px 20px !important;
            }

            .enhanced-toolbar {
                padding: 10px 15px;
                gap: 10px;
            }

            .toolbar-group {
                padding: 6px 8px;
                gap: 6px;
            }

            .toolbar-group label {
                font-size: 0.8rem;
            }

            .toolbar-select {
                font-size: 0.8rem;
                min-width: 60px;
            }

            .toolbar-input {
                width: 50px;
                font-size: 0.8rem;
            }

            .toolbar-group .action-btn {
                padding: 4px 8px;
                font-size: 0.8rem;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #cbd5e0, #a0aec0);
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #a0aec0, #718096);
        }

        /* Word文档上传区域样式 */
        .upload-zone {
            border: 2px dashed var(--border-color, #e9ecef);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-top: 10px;
        }

        .upload-zone:hover {
            border-color: var(--secondary-color, #11cdef);
            background: rgba(17, 205, 239, 0.05);
        }

        .upload-zone.dragover {
            border-color: var(--success-color, #2dce89);
            background: rgba(45, 206, 137, 0.08);
        }

        .file-info-display {
            background: rgba(17, 205, 239, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .file-info-display .d-flex {
            display: flex;
        }

        .file-info-display .align-items-center {
            align-items: center;
        }

        .file-info-display .me-2 {
            margin-right: 0.5rem;
        }

        .file-info-display .flex-grow-1 {
            flex: 1;
        }

        .file-info-display .fw-bold {
            font-weight: 600;
        }

        .file-info-display .small {
            font-size: 0.875rem;
        }

        .file-info-display .text-muted {
            color: var(--gray, #8898aa);
        }

        .file-info-display .text-primary {
            color: var(--primary-color, #5e72e4);
        }

        .file-info-display .btn {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            border: 1px solid transparent;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-info-display .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .file-info-display .btn-outline-danger {
            color: var(--danger-color, #f5365c);
            border-color: var(--danger-color, #f5365c);
            background: transparent;
        }

        .file-info-display .btn-outline-danger:hover {
            color: white;
            background-color: var(--danger-color, #f5365c);
        }

        .progress {
            height: 8px;
            background-color: #e9ecef;
            border-radius: 0.375rem;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background-color: var(--primary-color, #5e72e4);
            transition: width 0.6s ease;
        }

        .progress-bar-striped {
            background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
            background-size: 1rem 1rem;
        }

        .progress-bar-animated {
            animation: progress-bar-stripes 1s linear infinite;
        }

        @keyframes progress-bar-stripes {
            0% {
                background-position-x: 1rem;
            }
        }

        /* 动画入场效果 */
        .card {
            animation: fadeInUp 0.6s ease forwards;
            opacity: 0;
        }

        .card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .card:nth-child(3) {
            animation-delay: 0.3s;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 特殊提示样式 */
        .form-hint {
            font-size: 0.85rem;
            color: var(--gray);
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-hint i {
            font-size: 0.8rem;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--dark);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray);
            padding: 5px;
        }

        .modal-close:hover {
            color: var(--dark);
        }

        .paper-item {
            border: 1px solid #e5e7eb;
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .paper-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(94, 114, 228, 0.1);
        }

        .paper-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 10px;
        }

        .paper-meta {
            font-size: 0.9rem;
            color: var(--gray);
            margin-bottom: 10px;
        }

        .paper-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <!-- 草稿恢复提示条 -->
    <div id="draftNotification" class="draft-notification">
        <div class="draft-notification-content">
            <div class="draft-notification-text">
                <i class="fas fa-file-alt"></i>
                <span>发现之前保存的草稿，是否恢复？</span>
            </div>
            <div class="draft-notification-actions">
                <button class="draft-btn primary" onclick="restoreDraft()">
                    <i class="fas fa-undo"></i> 恢复草稿
                </button>
                <button class="draft-btn" onclick="ignoreDraft()">
                    <i class="fas fa-times"></i> 忽略
                </button>
                <button class="draft-close" onclick="closeDraftNotification()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 背景装饰 -->
    <div class="bg-decoration"></div>

    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-circle"></div>
        <div class="floating-circle"></div>
        <div class="floating-circle"></div>
        <div class="floating-circle"></div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="container">
        <!-- 头部标题 -->
        <div class="header">
            <a href="/" class="back-home-btn">
                <i class="fas fa-home"></i>
                返回首页
            </a>
            <h1><i class="fas fa-robot"></i> AI论文生成器</h1>
            <p>智能生成高质量学术论文，支持多种模板和自定义配置</p>
        </div>

        <!-- 论文配置区域 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-sliders-h"></i>
                <h2>论文配置</h2>
            </div>
            <div class="card-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">论文题目 <span class="required">*</span></label>
                        <input type="text" id="paperTitle" class="form-input" 
                               placeholder="请输入论文题目" 
                               value="基于Spring Boot的智能图书管理系统">
                        <div class="form-hint">
                            <i class="fas fa-info-circle"></i>
                            <span>题目应简洁明了，突出研究重点</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">研究领域 <span class="required">*</span></label>
                        <select id="researchField" class="form-select">
                            <option value="计算机科学" selected>计算机科学</option>
                            <option value="软件工程">软件工程</option>
                            <option value="人工智能">人工智能</option>
                            <option value="数据科学">数据科学</option>
                            <option value="信息安全">信息安全</option>
                            <option value="网络工程">网络工程</option>
                            <option value="大数据技术">大数据技术</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">论文类型</label>
                        <select id="paperType" class="form-select">
                            <option value="本科毕业论文" selected>本科毕业论文</option>
                            <option value="硕士学位论文">硕士学位论文</option>
                            <option value="博士学位论文">博士学位论文</option>
                            <option value="课程论文">课程论文</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">目标字数</label>
                        <select id="targetWords" class="form-select">
                            <option value="8000">8,000 字</option>
                            <option value="12000" selected>12,000 字</option>
                            <option value="15000">15,000 字</option>
                            <option value="20000">20,000 字</option>
                        </select>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">论文摘要</label>
                        <textarea id="paperAbstract" class="form-textarea" 
                                  placeholder="简要描述论文的研究内容、方法和主要结论..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">关键词</label>
                        <input type="text" id="keywords" class="form-input" 
                               placeholder="用分号分隔多个关键词">
                        <div class="form-hint">
                            <i class="fas fa-lightbulb"></i>
                            <span>例如：图书管理；Spring Boot；数据库设计</span>
                        </div>
                    </div>
                </div>

                <!-- 标题级别选择 -->
                <div class="title-level-section">
                    <label class="form-label">论文结构</label>
                    <div class="title-level-options">
                        <div class="level-option" data-level="two">
                            <div class="level-title">二级标题结构</div>
                            <div class="level-desc">简洁明了，适合本科论文，包含6个主要章节</div>
                        </div>
                        <div class="level-option active" data-level="three">
                            <div class="level-title">三级标题结构</div>
                            <div class="level-desc">详细完整，适合硕士及以上论文，多层次展开</div>
                        </div>
                        <div class="level-option" data-level="intelligent">
                            <div class="level-title">🧠 智能目录生成</div>
                            <div class="level-desc">AI根据您的描述智能分析，自动生成最适合的目录结构</div>
                        </div>
                    </div>
                </div>

                <!-- 智能目录生成区域 -->
                <div id="intelligentSection" class="intelligent-section" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-lightbulb text-warning"></i>
                            详细描述您的论文内容
                        </label>
                        <textarea id="paperDescription" class="form-textarea" rows="6"
                                  placeholder="请详细描述您要写的论文内容，包括：
1. 研究的主要问题或系统功能
2. 使用的技术栈或研究方法
3. 预期解决的问题或实现的目标
4. 论文的创新点或特色

例如：设计并实现一个基于Spring Boot的图书管理系统，使用MySQL数据库存储数据，Vue.js构建前端界面，实现图书借阅、归还、查询等功能，并加入智能推荐算法提升用户体验..."></textarea>
                        <div class="form-hint">
                            <i class="fas fa-info-circle"></i>
                            <span>描述越详细，AI生成的目录结构越准确！建议包含技术栈、功能模块、创新点等信息。</span>
                        </div>
                    </div>

                    <div class="intelligent-actions">
                        <button id="generateOutlineBtn" class="outline-btn">
                            <i class="fas fa-magic"></i>
                            AI智能分析生成目录
                        </button>
                    </div>

                    <!-- AI分析结果显示区域 -->
                    <div id="analysisResult" class="analysis-result" style="display: none;">
                        <div class="analysis-header">
                            <h4><i class="fas fa-brain text-primary"></i> AI分析结果</h4>
                        </div>
                        <div id="analysisContent">
                            <!-- AI分析内容将在这里显示 -->
                        </div>
                        <div class="analysis-actions">
                            <button id="confirmOutlineBtn" class="confirm-btn">
                                <i class="fas fa-check"></i>
                                确认使用此目录
                            </button>
                            <button id="regenerateOutlineBtn" class="regenerate-btn">
                                <i class="fas fa-redo"></i>
                                重新生成
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Word文档导入区域 -->
                <div class="form-group" style="margin-top: 25px;">
                    <label class="form-label">
                        <i class="fas fa-file-word text-primary"></i>
                        Word文档导入 (可选)
                    </label>
                    <div class="upload-zone" id="wordUploadZone" onclick="document.getElementById('wordFileInput').click()">
                        <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-3"></i>
                        <h6>点击选择或拖拽Word文档到此处</h6>
                        <p class="text-muted small mb-0">支持 .docx, .doc 格式文件</p>
                        <small class="text-muted">可导入现有Word文档进行格式调整</small>
                    </div>
                    <input type="file" id="wordFileInput" accept=".docx,.doc" style="display: none;">
                    
                    <!-- 文件信息显示 -->
                    <div id="wordFileInfo" class="file-info-display" style="display: none; margin-top: 15px;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-word me-2 text-primary"></i>
                            <div class="flex-grow-1">
                                <div class="fw-bold small" id="wordFileName"></div>
                                <small class="text-muted" id="wordFileSize"></small>
                            </div>
                            <button class="btn btn-sm btn-outline-danger" id="removeWordFileBtn">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="progress mt-2" id="wordProcessProgress" style="display: none;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 id="wordProgressBar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <div class="form-group requirements-section">
                    <label class="form-label">特殊要求</label>
                    <textarea id="requirements" class="form-textarea"
                              placeholder="如有特殊格式要求、内容重点或其他需求，请在此说明..."></textarea>
                </div>

                <button id="generateBtn" class="generate-btn normal-generate-btn">
                    <i class="fas fa-magic"></i>
                    生成目录
                </button>
            </div>
        </div>

        <!-- 进度显示区域 -->
        <div id="progressSection" class="progress-section">
            <div class="progress-header">
                <div class="progress-title">
                    <i class="fas fa-hourglass-half"></i> 正在生成论文
                </div>
                <div class="progress-percent">0%</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div class="progress-message">AI正在分析您的需求...</div>
        </div>

        <!-- 成功提示 -->
        <div id="successMessage" class="success-message">
            <i class="fas fa-check-circle"></i>
            <span>论文生成完成！您可以在下方编辑器中查看和编辑</span>
        </div>

        <!-- 编辑器区域 -->
        <div class="card editor-section">
            <div class="editor-header">
                <div class="editor-title">
                    <i class="fas fa-file-alt"></i>
                    智能编辑器
                </div>
                <div class="editor-actions">
                    <button class="action-btn" onclick="saveDraft()">
                        <i class="fas fa-save"></i> 保存草稿
                    </button>
                    <button class="action-btn" onclick="showMyPapers()">
                        <i class="fas fa-folder"></i> 我的论文
                    </button>
                    <button class="action-btn" onclick="exportToWord()">
                        <i class="fas fa-file-word"></i> 导出Word
                    </button>
                </div>
            </div>

            <!-- 增强工具栏 -->
            <div class="enhanced-toolbar">
                <div class="toolbar-group">
                    <label>字体:</label>
                    <select class="toolbar-select" id="fontFamily">
                        <option value="Arial">Arial</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="微软雅黑" selected>微软雅黑</option>
                        <option value="宋体">宋体</option>
                        <option value="黑体">黑体</option>
                        <option value="楷体">楷体</option>
                    </select>
                </div>

                <div class="toolbar-group">
                    <label>字号:</label>
                    <input type="number" class="toolbar-input" id="fontSize" value="16" min="8" max="72">
                </div>

                <div class="toolbar-group">
                    <label>行距:</label>
                    <select class="toolbar-select" id="lineHeight">
                        <option value="1.0">1.0</option>
                        <option value="1.15">1.15</option>
                        <option value="1.5" selected>1.5</option>
                        <option value="2.0">2.0</option>
                        <option value="2.5">2.5</option>
                    </select>
                </div>

                <div class="toolbar-group">
                    <button class="action-btn" onclick="insertTable()">
                        <i class="fas fa-table"></i> 表格
                    </button>
                </div>

                <div class="toolbar-group">
                    <button class="action-btn" onclick="insertMath()">
                        <i class="fas fa-square-root-alt"></i> 公式
                    </button>
                </div>

                <div class="toolbar-group">
                    <button class="action-btn" onclick="insertCodeBlock()">
                        <i class="fas fa-code"></i> 代码
                    </button>
                </div>

                <div class="toolbar-group">
                    <button class="action-btn" onclick="insertQuote()">
                        <i class="fas fa-quote-left"></i> 引用
                    </button>
                </div>

                <div class="toolbar-group">
                    <button class="action-btn" onclick="findReplace()">
                        <i class="fas fa-search"></i> 查找替换
                    </button>
                </div>

                <div class="toolbar-group">
                    <button class="action-btn" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> 全屏
                    </button>
                </div>

                <div class="toolbar-group">
                    <button class="action-btn" onclick="insertFootnote()">
                        <i class="fas fa-sticky-note"></i> 脚注
                    </button>
                </div>
            </div>

            <div id="editor" class="editor-container"></div>
        </div>
    </div>

    <!-- Quill.js JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/quill/1.3.7/quill.min.js"></script>
    
    <script>
        let quill;
        let currentTaskId = null;
        let isGenerating = false;

        // 初始化编辑器
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化');
            
            // 配置Quill工具栏
            const toolbarOptions = [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                [{ 'font': [] }],
                [{ 'size': ['small', false, 'large', 'huge'] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'script': 'sub'}, { 'script': 'super' }],
                ['blockquote', 'code-block'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                [{ 'direction': 'rtl' }],
                [{ 'align': [] }],
                ['link', 'image', 'video'],
                ['clean']
            ];

            quill = new Quill('#editor', {
                modules: {
                    toolbar: toolbarOptions
                },
                theme: 'snow',
                placeholder: '点击"开始生成论文"按钮，AI将为您智能生成高质量论文内容...'
            });

            // 设置简单的默认内容
            quill.setText('欢迎使用AI论文生成器\n\n请在上方配置您的论文需求，然后点击"开始生成论文"按钮。\n\n论文内容将在这里显示...');

            // 初始化增强功能
            initializeEnhancedFeatures();

            // 标题级别选择
            document.querySelectorAll('.level-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.level-option').forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');

                    // 显示/隐藏智能目录生成区域
                    const intelligentSection = document.getElementById('intelligentSection');
                    const cardElement = document.querySelector('.card');

                    if (this.dataset.level === 'intelligent') {
                        intelligentSection.style.display = 'block';
                        cardElement.classList.add('intelligent-mode');
                    } else {
                        intelligentSection.style.display = 'none';
                        cardElement.classList.remove('intelligent-mode');
                    }
                });
            });

            // 绑定生成按钮事件
            document.getElementById('generateBtn').addEventListener('click', generateOutline);

            // 绑定智能目录生成按钮事件
            document.getElementById('generateOutlineBtn').addEventListener('click', generateIntelligentOutline);

            // 绑定Word文档上传事件
            setupWordDocumentUpload();

            // 加载草稿
            loadDraft();
        });

        // Word文档处理类
        class DocumentProcessor {
            constructor() {
                this.mammoth = window.mammoth;
                this.docx = window.docx;
            }

            // 读取Word文档
            async readWordDocument(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = async (e) => {
                        try {
                            const arrayBuffer = e.target.result;
                            const result = await this.mammoth.convertToHtml({ arrayBuffer });
                            resolve(result.value);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('文件读取失败'));
                    reader.readAsArrayBuffer(file);
                });
            }

            // 生成Word文档
            async generateWordDocument(html, settings = {}) {
                try {
                    // 解析HTML内容
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const elements = doc.body.children;

                    const children = [];

                    for (let element of elements) {
                        if (element.tagName === 'H1') {
                            children.push(new this.docx.Paragraph({
                                text: element.textContent,
                                heading: this.docx.HeadingLevel.HEADING_1,
                                spacing: { before: 240, after: 120 }
                            }));
                        } else if (element.tagName === 'H2') {
                            children.push(new this.docx.Paragraph({
                                text: element.textContent,
                                heading: this.docx.HeadingLevel.HEADING_2,
                                spacing: { before: 180, after: 60 }
                            }));
                        } else if (element.tagName === 'H3') {
                            children.push(new this.docx.Paragraph({
                                text: element.textContent,
                                heading: this.docx.HeadingLevel.HEADING_3,
                                spacing: { before: 120, after: 60 }
                            }));
                        } else if (element.tagName === 'P') {
                            children.push(new this.docx.Paragraph({
                                text: element.textContent,
                                spacing: { 
                                    line: Math.round((settings.lineSpacing || 1.5) * 240),
                                    lineRule: this.docx.LineRuleType.AUTO
                                },
                                indent: { firstLine: 480 } // 首行缩进2字符
                            }));
                        }
                    }

                    const wordDoc = new this.docx.Document({
                        sections: [{
                            properties: {
                                page: {
                                    margin: {
                                        top: this.docx.convertMillimetersToTwip((settings.pageMargin || 2.5) * 10),
                                        right: this.docx.convertMillimetersToTwip((settings.pageMargin || 2.5) * 10),
                                        bottom: this.docx.convertMillimetersToTwip((settings.pageMargin || 2.5) * 10),
                                        left: this.docx.convertMillimetersToTwip((settings.pageMargin || 2.5) * 10)
                                    }
                                }
                            },
                            children: children
                        }]
                    });

                    return await this.docx.Packer.toBlob(wordDoc);
                } catch (error) {
                    console.error('生成Word文档失败:', error);
                    throw new Error('生成Word文档失败：' + error.message);
                }
            }
        }

        // 增强的智能标题识别器
        class EnhancedTitleDetector {
            constructor() {
                // 标题识别规则配置
                this.titleRules = {
                    // 明确的标题模式（高权重）
                    explicit: [
                        { pattern: /^第[一二三四五六七八九十\d]+章\s*(.*)/, level: 1, weight: 0.95 },
                        { pattern: /^第[一二三四五六七八九十\d]+节\s*(.*)/, level: 2, weight: 0.90 },
                        { pattern: /^\d+\.\s*(.+)/, level: 2, weight: 0.85 },
                        { pattern: /^\d+\.\d+\s*(.+)/, level: 3, weight: 0.85 },
                        { pattern: /^\d+\.\d+\.\d+\s*(.+)/, level: 4, weight: 0.80 },
                        { pattern: /^[一二三四五六七八九十]+、\s*(.*)/, level: 2, weight: 0.80 },
                        { pattern: /^\([一二三四五六七八九十\d]+\)\s*(.*)/, level: 3, weight: 0.75 },
                        { pattern: /^[①②③④⑤⑥⑦⑧⑨⑩]\s*(.*)/, level: 3, weight: 0.75 }
                    ],
                    // 学术标题关键词（中权重）
                    academic: [
                        { pattern: /^摘\s*要$/i, level: 1, weight: 0.95 },
                        { pattern: /^abstract$/i, level: 1, weight: 0.95 },
                        { pattern: /^引\s*言$/i, level: 1, weight: 0.90 },
                        { pattern: /^前\s*言$/i, level: 1, weight: 0.90 },
                        { pattern: /^绪\s*论$/i, level: 1, weight: 0.90 },
                        { pattern: /^结\s*论$/i, level: 1, weight: 0.90 },
                        { pattern: /^总\s*结$/i, level: 1, weight: 0.90 },
                        { pattern: /^参考文献$/i, level: 1, weight: 0.95 },
                        { pattern: /^致\s*谢$/i, level: 1, weight: 0.90 },
                        { pattern: /^附\s*录/i, level: 1, weight: 0.85 },
                        { pattern: /^目\s*录$/i, level: 1, weight: 0.95 }
                    ],
                    // 通用学术概念（中权重）
                    concepts: [
                        { keywords: ['研究背景', '研究现状', '文献综述'], level: 2, weight: 0.70 },
                        { keywords: ['研究方法', '实验方法', '技术路线'], level: 2, weight: 0.70 },
                        { keywords: ['系统设计', '架构设计', '模块设计'], level: 2, weight: 0.70 },
                        { keywords: ['实验结果', '结果分析', '性能分析'], level: 2, weight: 0.70 },
                        { keywords: ['问题分析', '需求分析', '可行性分析'], level: 2, weight: 0.70 },
                        { keywords: ['总结展望', '未来工作', '改进方向'], level: 2, weight: 0.70 }
                    ]
                };

                // 特征权重配置
                this.featureWeights = {
                    length: 0.20,           // 长度特征
                    position: 0.15,         // 位置特征
                    formatting: 0.25,       // 格式特征
                    semantic: 0.30,         // 语义特征
                    context: 0.10           // 上下文特征
                };

                // 标题长度阈值
                this.lengthThresholds = {
                    min: 2,         // 最小长度
                    max: 80,        // 最大长度
                    optimal: 30     // 最优长度
                };

                // 语义特征词汇库
                this.semanticFeatures = {
                    titleWords: [
                        '研究', '分析', '设计', '实现', '方法', '算法', '模型', '系统',
                        '技术', '应用', '评估', '优化', '改进', '创新', '探索', '开发',
                        '建构', '构建', '框架', '机制', '策略', '方案', '模式', '理论'
                    ],
                    fieldTerms: [
                        '计算机', '网络', '数据库', '人工智能', '机器学习', '深度学习',
                        '软件工程', '信息系统', '电子商务', '物联网', '云计算', '大数据'
                    ],
                    actionWords: [
                        '基于', '面向', '针对', '关于', '论', '浅谈', '探讨', '初探'
                    ]
                };
            }

            /**
             * 主要检测方法
             * @param {string} text - 待检测的文本
             * @param {Object} context - 上下文信息
             * @returns {Object} 检测结果
             */
            detectTitles(text, context = {}) {
                const lines = this.preprocessText(text);
                const results = [];

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    if (!line.trim()) continue;

                    const detection = this.analyzeLine(line, i, lines, context);
                    results.push({
                        text: line,
                        lineNumber: i,
                        isTitle: detection.isTitle,
                        confidence: detection.confidence,
                        level: detection.level,
                        features: detection.features,
                        reasoning: detection.reasoning
                    });
                }

                // 后处理优化
                return this.postProcessResults(results);
            }

            /**
             * 文本预处理
             */
            preprocessText(text) {
                return text
                    .split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);
            }

            /**
             * 分析单行文本
             */
            analyzeLine(line, position, allLines, context) {
                const features = this.extractFeatures(line, position, allLines, context);
                const ruleScore = this.applyRules(line);
                const mlScore = this.computeMLScore(features);
                
                // 综合评分
                const finalScore = this.combineScores(ruleScore, mlScore, features);
                
                const isTitle = finalScore.score > 0.5;
                const level = this.determineLevel(line, finalScore.score, ruleScore.level);

                return {
                    isTitle,
                    confidence: finalScore.score,
                    level,
                    features,
                    reasoning: finalScore.reasoning
                };
            }

            // 其他方法的简化版本，保留核心功能
            extractFeatures(line, position, allLines, context) {
                return {
                    length: { score: this.analyzeLengthScore(line) },
                    position: { score: this.analyzePositionScore(position, allLines.length) },
                    formatting: { score: this.analyzeFormattingScore(line) },
                    semantic: { score: this.analyzeSemanticScore(line) },
                    context: { score: this.analyzeContextScore(line, position, allLines) }
                };
            }

            analyzeLengthScore(line) {
                const length = line.length;
                if (length < 2 || length > 80) return 0;
                return Math.exp(-Math.pow(length - 30, 2) / (2 * Math.pow(10, 2)));
            }

            analyzePositionScore(position, totalLines) {
                const relativePosition = position / Math.max(totalLines - 1, 1);
                if (relativePosition < 0.1 || relativePosition > 0.9) return 0.8;
                if (relativePosition < 0.2 || relativePosition > 0.8) return 0.6;
                return 0.4;
            }

            analyzeFormattingScore(line) {
                let score = 0;
                if (!line.endsWith('。') && !line.endsWith('.')) score += 0.3;
                if (line.includes('？') || line.includes('?') || line.includes('：') || line.includes(':')) score += 0.2;
                if (/^\d+/.test(line)) score += 0.5;
                const commaCount = (line.match(/[，,]/g) || []).length;
                if (commaCount === 0) score += 0.2;
                else if (commaCount > 3) score -= 0.3;
                return Math.min(score, 1.0);
            }

            analyzeSemanticScore(line) {
                let score = 0;
                const titleWordMatches = this.semanticFeatures.titleWords.filter(word => line.includes(word));
                if (titleWordMatches.length > 0) score += Math.min(titleWordMatches.length * 0.2, 0.6);
                
                const fieldTermMatches = this.semanticFeatures.fieldTerms.filter(term => line.includes(term));
                if (fieldTermMatches.length > 0) score += Math.min(fieldTermMatches.length * 0.15, 0.3);
                
                const actionWordMatches = this.semanticFeatures.actionWords.filter(word => line.includes(word));
                if (actionWordMatches.length > 0) score += Math.min(actionWordMatches.length * 0.1, 0.2);
                
                return Math.min(score, 1.0);
            }

            analyzeContextScore(line, position, allLines) {
                let score = 0;
                const prevLine = position > 0 ? allLines[position - 1]?.trim() : '';
                const nextLine = position < allLines.length - 1 ? allLines[position + 1]?.trim() : '';
                
                if (!prevLine && nextLine) score += 0.3;
                if (!nextLine && prevLine) score += 0.2;
                
                const nextFewLines = allLines.slice(position + 1, position + 3);
                const hasContentAfter = nextFewLines.some(line => 
                    line && line.length > 50 && (line.includes('。') || line.includes('.'))
                );
                if (hasContentAfter) score += 0.4;
                
                return Math.min(score, 1.0);
            }

            applyRules(line) {
                let maxScore = 0;
                let level = 2;
                let matchedRule = null;

                // 检查明确模式
                for (const rule of this.titleRules.explicit) {
                    if (rule.pattern.test(line)) {
                        if (rule.weight > maxScore) {
                            maxScore = rule.weight;
                            level = rule.level;
                            matchedRule = rule;
                        }
                    }
                }

                // 检查学术标题
                for (const rule of this.titleRules.academic) {
                    if (rule.pattern.test(line)) {
                        if (rule.weight > maxScore) {
                            maxScore = rule.weight;
                            level = rule.level;
                            matchedRule = rule;
                        }
                    }
                }

                // 检查概念关键词
                for (const rule of this.titleRules.concepts) {
                    const hasKeyword = rule.keywords.some(keyword => line.includes(keyword));
                    if (hasKeyword && rule.weight > maxScore) {
                        maxScore = rule.weight;
                        level = rule.level;
                        matchedRule = rule;
                    }
                }

                return {
                    score: maxScore,
                    level,
                    rule: matchedRule
                };
            }

            computeMLScore(features) {
                let score = 0;
                score += features.length.score * this.featureWeights.length;
                score += features.position.score * this.featureWeights.position;
                score += features.formatting.score * this.featureWeights.formatting;
                score += features.semantic.score * this.featureWeights.semantic;
                score += features.context.score * this.featureWeights.context;
                return Math.min(score, 1.0);
            }

            combineScores(ruleScore, mlScore, features) {
                if (ruleScore.score > 0.8) {
                    return {
                        score: ruleScore.score,
                        reasoning: `规则匹配: ${ruleScore.rule?.pattern || '明确模式'}`
                    };
                }

                const combinedScore = (ruleScore.score * 0.6) + (mlScore * 0.4);
                let reasoning = [];
                if (ruleScore.score > 0.3) reasoning.push(`规则匹配(${(ruleScore.score * 100).toFixed(1)}%)`);
                if (mlScore > 0.3) reasoning.push(`特征分析(${(mlScore * 100).toFixed(1)}%)`);

                return {
                    score: combinedScore,
                    reasoning: reasoning.join(' + ') || '综合分析'
                };
            }

            determineLevel(line, confidence, ruleLevel) {
                if (ruleLevel) return ruleLevel;
                if (/^第.*章/.test(line) || /^Chapter/i.test(line)) return 1;
                if (/^\d+\./.test(line)) return 2;
                if (/^\d+\.\d+/.test(line)) return 3;
                if (/^\d+\.\d+\.\d+/.test(line)) return 4;
                if (confidence > 0.8) return 1;
                if (confidence > 0.6) return 2;
                return 3;
            }

            postProcessResults(results) {
                // 简化的后处理
                for (let i = 1; i < results.length - 1; i++) {
                    const current = results[i];
                    const prev = results[i - 1];
                    const next = results[i + 1];

                    if (!current.isTitle && prev.isTitle && next.isTitle) {
                        if (current.confidence > 0.3 && current.text.length < 50) {
                            current.isTitle = true;
                            current.confidence = Math.min(current.confidence + 0.2, 0.9);
                            current.reasoning += ' + 上下文调整';
                        }
                    }
                }
                return results;
            }

            // 应用智能标题识别到HTML内容
            applySmartTitleDetection(htmlContent) {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = htmlContent;
                
                // 提取纯文本进行分析
                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                const titleResults = this.detectTitles(textContent);
                
                // 将识别结果应用到HTML
                const lines = textContent.split('\n').filter(line => line.trim());
                const paragraphs = tempDiv.querySelectorAll('p');
                
                let lineIndex = 0;
                paragraphs.forEach(p => {
                    const text = p.textContent.trim();
                    if (!text) return;
                    
                    const matchingResult = titleResults.find(result => 
                        result.text === text && result.isTitle
                    );
                    
                    if (matchingResult) {
                        const headingLevel = Math.min(matchingResult.level, 3);
                        const heading = document.createElement(`h${headingLevel}`);
                        heading.textContent = text;
                        heading.title = `置信度: ${(matchingResult.confidence * 100).toFixed(1)}% - ${matchingResult.reasoning}`;
                        p.parentNode.replaceChild(heading, p);
                    }
                });
                
                return tempDiv.innerHTML;
            }
        }

        // 设置Word文档上传功能
        function setupWordDocumentUpload() {
            const wordFileInput = document.getElementById('wordFileInput');
            const wordUploadZone = document.getElementById('wordUploadZone');
            const removeWordFileBtn = document.getElementById('removeWordFileBtn');

            // 文件选择事件
            wordFileInput.addEventListener('change', handleWordFileSelect);

            // 拖拽事件
            wordUploadZone.addEventListener('dragover', handleWordDragOver);
            wordUploadZone.addEventListener('dragleave', handleWordDragLeave);
            wordUploadZone.addEventListener('drop', handleWordFileDrop);

            // 移除文件事件
            removeWordFileBtn.addEventListener('click', removeWordFile);
        }

        // 处理Word文件选择
        function handleWordFileSelect(event) {
            const file = event.target.files[0];
            if (file) processWordFile(file);
        }

        // 处理拖拽悬停
        function handleWordDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        // 处理拖拽离开
        function handleWordDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        // 处理文件拖拽放下
        function handleWordFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = event.dataTransfer.files;
            if (files.length > 0) processWordFile(files[0]);
        }

        // 处理Word文件
        async function processWordFile(file) {
            // 文件验证
            const allowedTypes = ['.docx', '.doc'];
            const fileExt = '.' + file.name.split('.').pop().toLowerCase();
            
            if (!allowedTypes.includes(fileExt)) {
                showToast('不支持的文件格式，请上传Word文档', 'error');
                return;
            }

            if (file.size > 10 * 1024 * 1024) {
                showToast('文件过大，请上传小于10MB的文件', 'error');
                return;
            }

            // 显示文件信息
            showWordFileInfo(file);
            
            try {
                showLoading(true);
                showWordProcessProgress(true);
                
                const processor = new DocumentProcessor();
                const htmlContent = await processor.readWordDocument(file);

                // 应用智能标题识别
                const titleDetector = new EnhancedTitleDetector();
                const enhancedContent = titleDetector.applySmartTitleDetection(htmlContent);

                // 将内容加载到Quill编辑器
                quill.root.innerHTML = enhancedContent;
                
                hideLoading();
                showWordProcessProgress(false);
                showToast('Word文档加载成功！已应用智能标题识别', 'success');
                
            } catch (error) {
                hideLoading();
                showWordProcessProgress(false);
                showToast('Word文档读取失败：' + error.message, 'error');
                console.error('Word文档处理错误:', error);
            }
        }

        // 显示Word文件信息
        function showWordFileInfo(file) {
            document.getElementById('wordFileName').textContent = file.name;
            document.getElementById('wordFileSize').textContent = formatFileSize(file.size);
            document.getElementById('wordFileInfo').style.display = 'block';
        }

        // 显示Word处理进度
        function showWordProcessProgress(show) {
            const progressContainer = document.getElementById('wordProcessProgress');
            const progressBar = document.getElementById('wordProgressBar');
            
            if (show) {
                progressContainer.style.display = 'block';
                progressBar.style.width = '0%';
                
                // 模拟进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 100) {
                        progress = 100;
                        clearInterval(interval);
                    }
                    progressBar.style.width = progress + '%';
                }, 300);
            } else {
                progressContainer.style.display = 'none';
            }
        }

        // 移除Word文件
        function removeWordFile() {
            document.getElementById('wordFileInfo').style.display = 'none';
            document.getElementById('wordFileInput').value = '';
            showWordProcessProgress(false);
            showToast('Word文件已移除', 'info');
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 增强的导出Word功能
        async function exportToWord() {
            try {
                const content = quill.getContents();
                const title = document.getElementById('paperTitle').value || '未命名论文';
                
                if (!content.ops || content.ops.length === 0) {
                    showToast('请先生成论文内容', 'error');
                    return;
                }

                showLoading(true);

                // 使用docx.js直接生成Word文档
                const processor = new DocumentProcessor();
                const htmlContent = quill.root.innerHTML;
                
                const settings = {
                    lineSpacing: 1.5,
                    pageMargin: 2.5
                };

                const blob = await processor.generateWordDocument(htmlContent, settings);
                
                // 下载文件
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${title}_${new Date().toISOString().slice(0,10)}.docx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                showToast('Word文档导出成功！');
            } catch (error) {
                console.error('导出失败:', error);
                showToast('导出功能处理中，您可以复制内容到Word中', 'error');
            } finally {
                showLoading(false);
            }
        }

        // 智能目录生成函数
        async function generateIntelligentOutline() {
            console.log('开始智能生成目录');

            const title = document.getElementById('paperTitle').value.trim();
            const field = document.getElementById('researchField').value;
            const paperType = document.getElementById('paperType').value;
            const targetWords = parseInt(document.getElementById('targetWords').value);
            const abstract = document.getElementById('paperAbstract').value.trim();
            const keywords = document.getElementById('keywords').value.trim();
            const paperDescription = document.getElementById('paperDescription').value.trim();

            // 验证必填项
            if (!title || !field) {
                showToast('请填写论文题目和研究领域', 'error');
                return;
            }

            if (!abstract) {
                showToast('请填写论文摘要，这有助于AI更好地理解您的研究内容', 'error');
                return;
            }

            if (!paperDescription) {
                showToast('请详细描述您的论文内容，这是AI生成准确目录的关键信息', 'error');
                return;
            }

            try {
                // 显示加载状态
                const generateBtn = document.getElementById('generateOutlineBtn');
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<div class="loading-spinner"></div> AI正在智能分析...';

                // 调用智能目录生成API
                const response = await fetch('/api/generate-intelligent-outline', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        field: field,
                        paper_type: paperType,
                        total_words: targetWords,
                        abstract: abstract,
                        keywords: keywords,
                        special_requirements: paperDescription
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 显示目录预览界面
                    showOutlinePreview(result.outline, {
                        title, field, paperType, targetWords, useThreeLevel: false, isIntelligent: true
                    });
                    showToast('AI智能目录生成成功！', 'success');
                } else {
                    showToast(result.message || 'AI目录生成失败', 'error');
                }

            } catch (error) {
                console.error('智能生成目录失败:', error);
                showToast('AI目录生成失败，请检查网络连接或稍后重试', 'error');
            } finally {
                // 恢复按钮状态
                const generateBtn = document.getElementById('generateOutlineBtn');
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> AI智能分析生成目录';
            }
        }

        // 第一步：生成目录
        async function generateOutline() {
            console.log('开始生成目录');

            const title = document.getElementById('paperTitle').value.trim();
            const field = document.getElementById('researchField').value;
            const paperType = document.getElementById('paperType').value;
            const targetWords = parseInt(document.getElementById('targetWords').value);
            const useThreeLevel = document.querySelector('.level-option.active').dataset.level === 'three';

            // 验证必填项
            if (!title || !field) {
                showToast('请填写论文题目和研究领域', 'error');
                return;
            }

            try {
                // 显示加载状态
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<div class="loading-spinner"></div> 生成目录中...';

                // 调用目录生成API
                const response = await fetch('/api/generate-outline', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        field: field,
                        type: paperType,
                        words: targetWords,
                        use_three_level: useThreeLevel
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 显示目录预览界面
                    showOutlinePreview(result.outline, {
                        title, field, paperType, targetWords, useThreeLevel
                    });
                } else {
                    showToast(result.message || '目录生成失败', 'error');
                }

            } catch (error) {
                console.error('生成目录失败:', error);
                showToast('目录生成失败，请稍后重试', 'error');
            } finally {
                // 恢复按钮状态
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> 生成目录';
            }
        }

        // 显示目录预览界面
        function showOutlinePreview(outline, paperInfo) {
            // 创建目录预览模态框
            const modal = document.createElement('div');
            modal.className = 'outline-modal';
            modal.innerHTML = `
                <div class="outline-modal-content">
                    <div class="outline-header">
                        <h3><i class="fas fa-file-alt"></i> 论文目录预览</h3>
                        <p class="outline-subtitle">请检查并编辑目录结构，然后开始生成论文</p>
                    </div>

                    <div class="outline-info">
                        <div class="info-item">
                            <span class="label">论文题目：</span>
                            <span class="value">${paperInfo.title}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">研究领域：</span>
                            <span class="value">${paperInfo.field}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">目标字数：</span>
                            <span class="value">${paperInfo.targetWords}字</span>
                        </div>
                        <div class="info-item">
                            <span class="label">标题级别：</span>
                            <span class="value">${paperInfo.useThreeLevel ? '三级标题' : '二级标题'}</span>
                        </div>
                    </div>

                    <div class="outline-editor">
                        <h4>目录结构 <small>(可编辑)</small></h4>
                        <div class="outline-list" id="outlineList">
                            ${generateOutlineHTML(outline)}
                        </div>
                        <div class="outline-actions">
                            <button type="button" class="btn-secondary add-section-btn" onclick="addSection()">
                                <i class="fas fa-plus"></i> 添加章节
                            </button>
                        </div>
                    </div>

                    <div class="outline-footer">
                        <button type="button" class="btn-secondary outline-cancel-btn" onclick="closeOutlineModal()">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="button" class="btn-primary outline-confirm-btn" onclick="confirmOutlineAndGenerate()">
                            <i class="fas fa-magic"></i> 确认目录并生成论文
                        </button>
                    </div>
                </div>
            `;

            // 添加样式
            if (!document.getElementById('outline-modal-styles')) {
                const styles = document.createElement('style');
                styles.id = 'outline-modal-styles';
                styles.textContent = `
                    .outline-modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 10000;
                    }

                    .outline-modal-content {
                        background: white;
                        border-radius: 16px;
                        width: 90%;
                        max-width: 800px;
                        max-height: 90vh;
                        overflow-y: auto;
                        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15), 0 10px 40px rgba(0, 0, 0, 0.1);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                    }

                    .outline-header {
                        padding: 20px 24px 16px;
                        border-bottom: 1px solid #e9ecef;
                        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                    }

                    .outline-header h3 {
                        margin: 0 0 8px;
                        color: #2c3e50;
                        font-size: 18px;
                        font-weight: 600;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .outline-header h3 i {
                        color: #007bff;
                        font-size: 16px;
                    }

                    .outline-subtitle {
                        margin: 0;
                        color: var(--gray);
                        font-size: 14px;
                    }

                    .outline-info {
                        padding: 20px 24px;
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 16px;
                        border-bottom: 1px solid #dee2e6;
                    }

                    .info-item {
                        display: flex;
                        align-items: center;
                        padding: 8px 12px;
                        background: white;
                        border-radius: 6px;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    }

                    .info-item .label {
                        font-weight: 600;
                        color: #495057;
                        margin-right: 8px;
                        min-width: 80px;
                        font-size: 13px;
                    }

                    .info-item .value {
                        color: #007bff;
                        font-weight: 500;
                    }

                    .outline-editor {
                        padding: 24px;
                    }

                    .outline-editor h4 {
                        margin: 0 0 16px;
                        color: var(--dark);
                    }

                    .outline-list {
                        border: 1px solid #e9ecef;
                        border-radius: 8px;
                        background: white;
                    }

                    .outline-item {
                        display: flex;
                        align-items: center;
                        padding: 14px 20px;
                        border-bottom: 1px solid #f1f3f4;
                        transition: all 0.2s ease;
                        background: white;
                    }

                    .outline-item:hover {
                        background: #f8f9fa;
                        border-left: 3px solid #007bff;
                        padding-left: 17px;
                    }

                    .outline-item:last-child {
                        border-bottom: none;
                    }

                    .outline-item-content {
                        flex: 1;
                        display: grid;
                        grid-template-columns: 2fr 1fr 3fr;
                        gap: 16px;
                        align-items: center;
                    }

                    .outline-item input {
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                        padding: 8px 12px;
                        font-size: 14px;
                        transition: border-color 0.2s ease;
                        background: #fafbfc;
                    }

                    .outline-item input:focus {
                        outline: none;
                        border-color: #007bff;
                        background: white;
                        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
                    }

                    .outline-item-actions {
                        margin-left: 12px;
                    }

                    .outline-item-actions button {
                        background: none;
                        border: none;
                        color: #6c757d;
                        cursor: pointer;
                        padding: 6px 10px;
                        border-radius: 6px;
                        margin-left: 4px;
                        transition: all 0.2s ease;
                        font-size: 14px;
                    }

                    .outline-item-actions button:hover {
                        background: #fee;
                        color: #dc3545;
                        transform: scale(1.05);
                    }

                    .outline-actions {
                        margin-top: 20px;
                        text-align: center;
                        padding: 16px 20px;
                        background: #f8f9fa;
                        border-top: 1px solid #e9ecef;
                    }

                    .btn-secondary.add-section-btn {
                        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
                        color: white !important;
                        border: none !important;
                        padding: 10px 20px !important;
                        border-radius: 8px !important;
                        font-weight: 500 !important;
                        transition: all 0.2s ease !important;
                        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2) !important;
                        transform: none !important;
                    }

                    .btn-secondary.add-section-btn:hover {
                        transform: translateY(-1px) !important;
                        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
                        background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
                    }

                    .btn-secondary.outline-cancel-btn {
                        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
                        color: white !important;
                        border: none !important;
                        padding: 10px 20px !important;
                        border-radius: 8px !important;
                        font-weight: 500 !important;
                        transition: all 0.2s ease !important;
                        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2) !important;
                        transform: none !important;
                    }

                    .btn-secondary.outline-cancel-btn:hover {
                        transform: translateY(-1px) !important;
                        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3) !important;
                        background: linear-gradient(135deg, #5a6268 0%, #495057 100%) !important;
                    }

                    .btn-primary.outline-confirm-btn {
                        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
                        color: white !important;
                        border: none !important;
                        padding: 10px 24px !important;
                        border-radius: 8px !important;
                        font-weight: 600 !important;
                        transition: all 0.2s ease !important;
                        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2) !important;
                        font-size: 14px !important;
                        transform: none !important;
                    }

                    .btn-primary.outline-confirm-btn:hover {
                        transform: translateY(-1px) !important;
                        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
                        background: linear-gradient(135deg, #1e7e34 0%, #155724 100%) !important;
                    }

                    .outline-footer {
                        padding: 20px 24px;
                        border-top: 1px solid #e9ecef;
                        display: flex;
                        justify-content: flex-end;
                        gap: 12px;
                        background: #fafbfc;
                        border-radius: 0 0 16px 16px;
                    }
                `;
                document.head.appendChild(styles);
            }

            // 存储当前的论文信息和目录
            window.currentPaperInfo = paperInfo;
            window.currentOutline = outline;

            document.body.appendChild(modal);
        }

        // 生成目录HTML
        function generateOutlineHTML(outline) {
            return outline.map((section, index) => `
                <div class="outline-item" data-index="${index}">
                    <div class="outline-item-content">
                        <input type="text" value="${section.name}" placeholder="章节名称" onchange="updateSectionName(${index}, this.value)">
                        <input type="number" value="${section.words}" placeholder="字数" min="100" max="5000" onchange="updateSectionWords(${index}, this.value)">
                        <input type="text" value="${section.description}" placeholder="章节描述" onchange="updateSectionDescription(${index}, this.value)">
                    </div>
                    <div class="outline-item-actions">
                        <button type="button" onclick="removeSection(${index})" title="删除章节">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 目录编辑相关函数
        function updateSectionName(index, value) {
            window.currentOutline[index].name = value;
        }

        function updateSectionWords(index, value) {
            window.currentOutline[index].words = parseInt(value) || 500;
        }

        function updateSectionDescription(index, value) {
            window.currentOutline[index].description = value;
        }

        function removeSection(index) {
            if (window.currentOutline.length <= 3) {
                showToast('至少需要保留3个章节', 'warning');
                return;
            }
            window.currentOutline.splice(index, 1);
            document.getElementById('outlineList').innerHTML = generateOutlineHTML(window.currentOutline);
        }

        function addSection() {
            const newSection = {
                name: "新章节",
                words: 1000,
                description: "请填写章节描述",
                level: 2
            };
            window.currentOutline.push(newSection);
            document.getElementById('outlineList').innerHTML = generateOutlineHTML(window.currentOutline);
        }

        function closeOutlineModal() {
            const modal = document.querySelector('.outline-modal');
            if (modal) {
                modal.remove();
            }
        }

        function confirmOutlineAndGenerate() {
            // 关闭模态框
            closeOutlineModal();

            // 开始生成论文
            generatePaperWithOutline();
        }

        // 第二步：根据确认的目录生成论文
        async function generatePaperWithOutline() {
            console.log('开始根据目录生成论文');

            if (isGenerating) {
                console.log('正在生成中，忽略重复请求');
                return;
            }

            const abstract = document.getElementById('paperAbstract').value.trim();
            const keywords = document.getElementById('keywords').value.trim();
            const requirements = document.getElementById('requirements').value.trim();

            // 显示进度条
            const progressSection = document.getElementById('progressSection');
            const generateBtn = document.getElementById('generateBtn');
            const progressFill = document.querySelector('.progress-fill');
            const progressPercent = document.querySelector('.progress-percent');
            const progressMessage = document.querySelector('.progress-message');

            isGenerating = true;
            progressSection.style.display = 'block';
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<div class="loading-spinner"></div> 生成中...';

            try {
                // 准备数据
                const formData = {
                    title: window.currentPaperInfo.title,
                    field: window.currentPaperInfo.field,
                    type: window.currentPaperInfo.paperType,
                    abstract: abstract,
                    keywords: keywords,
                    requirements: requirements,
                    outline: window.currentOutline  // 使用用户确认的目录
                };

                console.log('发送论文生成请求:', formData);

                // 发送生成请求
                const response = await fetch('/api/generate-paper', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                console.log('论文生成响应:', result);

                if (result.success) {
                    currentTaskId = result.task_id;
                    console.log('设置 currentTaskId:', currentTaskId);
                    showToast('论文生成任务已启动', 'success');

                    // 验证 currentTaskId 不为空
                    if (!currentTaskId) {
                        throw new Error('服务器未返回有效的任务ID');
                    }

                    // 开始轮询进度
                    pollProgress(currentTaskId, progressFill, progressPercent, progressMessage);
                } else {
                    throw new Error(result.message || '生成失败');
                }

            } catch (error) {
                console.error('生成论文失败:', error);
                showToast('生成失败: ' + error.message, 'error');

                // 重置状态
                isGenerating = false;
                progressSection.style.display = 'none';
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> 生成目录';
            }
        }

        // 保留原有的生成论文函数以兼容
        async function generatePaper() {
            console.log('开始生成论文');

            if (isGenerating) {
                console.log('正在生成中，忽略重复请求');
                return;
            }

            const title = document.getElementById('paperTitle').value.trim();
            const field = document.getElementById('researchField').value;
            const paperType = document.getElementById('paperType').value;
            const targetWords = parseInt(document.getElementById('targetWords').value);
            const abstract = document.getElementById('paperAbstract').value.trim();
            const keywords = document.getElementById('keywords').value.trim();
            const requirements = document.getElementById('requirements').value.trim();
            const useThreeLevel = document.querySelector('.level-option.active').dataset.level === 'three';

            // 验证必填项
            if (!title || !field) {
                showToast('请填写论文题目和研究领域', 'error');
                return;
            }

            // 显示进度条
            const progressSection = document.getElementById('progressSection');
            const generateBtn = document.getElementById('generateBtn');
            const progressFill = document.querySelector('.progress-fill');
            const progressPercent = document.querySelector('.progress-percent');
            const progressMessage = document.querySelector('.progress-message');

            isGenerating = true;
            progressSection.style.display = 'block';
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<div class="loading-spinner"></div> 生成中...';

            try {
                // 准备数据
                const formData = {
                    title: title,
                    field: field,
                    type: paperType,
                    words: targetWords,
                    abstract: abstract,
                    keywords: keywords,
                    requirements: requirements,
                    useThreeLevel: useThreeLevel
                };

                // 启动生成任务
                const response = await fetch('/api/generate-paper', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    throw new Error(`请求失败 (${response.status})`);
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.message || '启动生成任务失败');
                }

                const taskId = result.task_id;
                console.log('任务ID:', taskId);

                // 开始轮询进度
                await pollProgress(taskId, progressFill, progressPercent, progressMessage);

            } catch (error) {
                console.error('生成论文失败:', error);
                showToast('生成失败: ' + error.message, 'error');
                
                // 模拟生成成功（用于演示）
                simulateGeneration(progressFill, progressPercent, progressMessage);
            }
        }

        // 模拟生成过程（用于演示）
        function simulateGeneration(progressFill, progressPercent, progressMessage) {
            // 验证参数
            if (!progressFill || !progressPercent || !progressMessage) {
                console.error('simulateGeneration: 缺少必要的DOM元素参数');
                return;
            }

            let progress = 0;
            const messages = [
                'AI正在分析您的需求...',
                '正在构建论文大纲...',
                '生成引言部分...',
                '撰写文献综述...',
                '编写研究方法...',
                '分析研究结果...',
                '撰写结论部分...',
                '优化论文内容...',
                '即将完成...'
            ];

            const progressInterval = setInterval(() => {
                if (progress < 90) {
                    progress += Math.random() * 15;
                    progress = Math.min(progress, 90);
                    progressFill.style.width = progress + '%';
                    progressPercent.textContent = Math.floor(progress) + '%';
                    
                    const messageIndex = Math.floor(progress / 11);
                    progressMessage.textContent = messages[Math.min(messageIndex, messages.length - 1)];
                }
            }, 1000);

            // 完成生成
            setTimeout(() => {
                clearInterval(progressInterval);
                progressFill.style.width = '100%';
                progressPercent.textContent = '100%';
                progressMessage.textContent = '论文生成完成！';

                setTimeout(() => {
                    document.getElementById('progressSection').style.display = 'none';
                    document.getElementById('successMessage').style.display = 'flex';
                    
                    // 生成示例论文内容
                    const title = document.getElementById('paperTitle').value;
                    const field = document.getElementById('researchField').value;
                    const paperContent = generateSampleContent(title, field);
                    quill.setText(''); // 清空编辑器
                    quill.clipboard.dangerouslyPasteHTML(paperContent);
                    
                    // 恢复按钮状态
                    isGenerating = false;
                    const generateBtn = document.getElementById('generateBtn');
                    generateBtn.disabled = false;
                    generateBtn.innerHTML = '<i class="fas fa-magic"></i> 重新生成论文';

                    // 自动保存
                    setTimeout(() => {
                        savePaperToDatabase();
                        saveDraft();
                    }, 1000);

                    // 3秒后隐藏成功提示
                    setTimeout(() => {
                        document.getElementById('successMessage').style.display = 'none';
                    }, 3000);

                    showToast('论文生成成功！', 'success');
                }, 1000);
            }, 8000);
        }

        // 轮询进度（真实API）
        async function pollProgress(taskId, progressFill, progressPercent, progressMessage) {
            console.log('开始轮询进度，taskId:', taskId);

            // 验证参数
            if (!taskId) {
                console.error('pollProgress: taskId 为空');
                return;
            }
            if (!progressFill || !progressPercent || !progressMessage) {
                console.error('pollProgress: 缺少必要的DOM元素参数');
                return;
            }

            const poll = async () => {
                try {
                    console.log('轮询进度 API:', `/api/paper-progress/${taskId}`);
                    const response = await fetch(`/api/paper-progress/${taskId}`);
                    if (!response.ok) {
                        throw new Error(`查询进度失败 (${response.status})`);
                    }

                    const data = await response.json();
                    if (!data.success) {
                        throw new Error(data.message || '查询进度失败');
                    }

                    // 更新进度显示
                    progressFill.style.width = data.progress + '%';
                    progressPercent.textContent = Math.floor(data.progress) + '%';
                    progressMessage.textContent = data.message || '正在生成...';

                    // 检查是否完成
                    if (data.status === 'completed') {
                        if (data.content) {
                            quill.setText('');
                            quill.clipboard.dangerouslyPasteHTML(data.content);
                            showToast('论文生成完成！', 'success');
                            
                            // 自动保存
                            setTimeout(() => {
                                savePaperToDatabase();
                                saveDraft();
                            }, 1000);
                        }

                        // 重置状态
                        isGenerating = false;
                        const generateBtn = document.getElementById('generateBtn');
                        generateBtn.disabled = false;
                        generateBtn.innerHTML = '<i class="fas fa-magic"></i> 重新生成论文';
                        
                        setTimeout(() => {
                            document.getElementById('progressSection').style.display = 'none';
                        }, 2000);
                        
                        return;
                    }

                    // 检查是否出错
                    if (data.status === 'error') {
                        throw new Error(data.error || '生成过程中出现错误');
                    }

                    // 继续轮询
                    if (data.status === 'running') {
                        setTimeout(poll, 3000);
                    }

                } catch (error) {
                    console.error('轮询进度失败:', error);
                    showToast('生成失败: ' + error.message, 'error');
                    
                    // 回退到模拟模式
                    simulateGeneration(progressFill, progressPercent, progressMessage);
                }
            };

            poll();
        }

        // 生成示例论文内容
        function generateSampleContent(title, field) {
            return `
<h1 style="text-align: center;">${title}</h1>

<h2>摘要</h2>
<p>本研究针对${field}领域的关键问题，设计并实现了${title}。通过深入分析需求，采用现代化的技术架构，成功构建了功能完善的系统。研究结果表明，该系统在性能、安全性和用户体验方面都达到了预期目标，为相关领域的发展提供了有价值的参考。</p>

<p><strong>关键词：</strong>${field}；系统设计；技术创新；应用研究</p>

<h2>第一章 绪论</h2>
<h3>1.1 研究背景</h3>
<p>随着信息技术的快速发展，${field}领域正经历着深刻的变革。传统的方法和系统已经难以满足日益增长的需求，迫切需要创新的解决方案。</p>

<h3>1.2 研究意义</h3>
<p>本研究具有重要的理论意义和实践价值。从理论层面上，丰富了${field}的研究内容；从实践角度看，为相关应用提供了技术支撑。</p>

<h3>1.3 研究内容</h3>
<p>本研究主要围绕${title}展开，包括需求分析、系统设计、功能实现和测试优化等方面。</p>

<h2>第二章 相关技术</h2>
<h3>2.1 技术选型</h3>
<p>系统采用了当前主流的技术栈，确保了系统的先进性和可维护性。</p>

<h3>2.2 理论基础</h3>
<p>基于软件工程的基本原理，遵循模块化、高内聚低耦合的设计原则。</p>

<h2>第三章 系统分析与设计</h2>
<h3>3.1 需求分析</h3>
<p>通过深入的需求调研，明确了系统的功能性需求和非功能性需求。</p>

<h3>3.2 系统设计</h3>
<p>采用分层架构设计，确保了系统的可扩展性和可维护性。</p>

<h2>第四章 系统实现</h2>
<h3>4.1 开发环境</h3>
<p>详细介绍了开发环境的配置和搭建过程。</p>

<h3>4.2 核心功能实现</h3>
<p>阐述了系统各个核心功能模块的具体实现方法和技术细节。</p>

<h2>第五章 系统测试</h2>
<h3>5.1 测试方案</h3>
<p>制定了全面的测试方案，包括功能测试、性能测试和安全性测试。</p>

<h3>5.2 测试结果</h3>
<p>测试结果表明，系统各项功能正常，性能指标达到预期要求。</p>

<h2>第六章 总结与展望</h2>
<h3>6.1 研究总结</h3>
<p>本研究成功实现了预期目标，系统功能完善，性能优良。</p>

<h3>6.2 未来展望</h3>
<p>未来可以在系统功能扩展、性能优化等方面继续深入研究。</p>

<h2>参考文献</h2>
<p>[1] 张三, 李四. ${field}技术研究[J]. 计算机科学, 2024.</p>
<p>[2] 王五, 赵六. 现代软件开发方法[M]. 清华大学出版社, 2024.</p>

<h2>致谢</h2>
<p>感谢指导老师的悉心指导，感谢同学们的帮助与支持。</p>`;
        }

        // 保存草稿
        function saveDraft() {
            try {
                const content = quill.getContents();
                const title = document.getElementById('paperTitle').value || '未命名论文';
                
                const draft = {
                    title: title,
                    content: content,
                    timestamp: new Date().toISOString()
                };
                
                localStorage.setItem('paperDraft', JSON.stringify(draft));
                showToast('草稿已保存');
                console.log('草稿已保存到本地存储');
            } catch (error) {
                console.error('保存草稿失败:', error);
                showToast('保存草稿失败', 'error');
            }
        }

        // 加载草稿
        function loadDraft() {
            try {
                const draft = localStorage.getItem('paperDraft');
                if (draft) {
                    const data = JSON.parse(draft);

                    // 显示优雅的提示条而不是弹窗
                    showDraftNotification();

                    // 将草稿数据存储到全局变量，供后续恢复使用
                    window.savedDraft = data;
                }
            } catch (error) {
                console.error('加载草稿失败:', error);
            }
        }

        // 显示草稿提示条
        function showDraftNotification() {
            const notification = document.getElementById('draftNotification');

            // 延迟一点显示，让页面先加载完成
            setTimeout(() => {
                notification.classList.add('show');
                document.body.classList.add('draft-notification-visible');

                // 10秒后自动隐藏
                setTimeout(() => {
                    if (notification.classList.contains('show')) {
                        closeDraftNotification();
                    }
                }, 10000);
            }, 500);
        }

        // 关闭草稿提示条
        function closeDraftNotification() {
            const notification = document.getElementById('draftNotification');
            notification.classList.remove('show');
            document.body.classList.remove('draft-notification-visible');
        }

        // 恢复草稿
        function restoreDraft() {
            try {
                if (window.savedDraft) {
                    const data = window.savedDraft;

                    if (data.title) {
                        document.getElementById('paperTitle').value = data.title;
                    }
                    if (data.content) {
                        quill.setContents(data.content);
                    }

                    showToast('草稿已恢复', 'success');
                    console.log('草稿已恢复');
                }
            } catch (error) {
                console.error('恢复草稿失败:', error);
                showToast('恢复草稿失败', 'error');
            } finally {
                closeDraftNotification();
            }
        }

        // 忽略草稿
        function ignoreDraft() {
            // 可以选择删除草稿或保留
            // localStorage.removeItem('paperDraft'); // 如果要删除草稿
            closeDraftNotification();
            showToast('已忽略草稿', 'info');
        }

        // 保存论文到数据库
        async function savePaperToDatabase() {
            try {
                console.log('开始保存论文到数据库...');
                
                const content = quill.getContents();
                const title = document.getElementById('paperTitle').value || '未命名论文';
                const field = document.getElementById('researchField').value || '';
                const paperType = document.getElementById('paperType').value || '本科毕业论文';
                const targetWords = parseInt(document.getElementById('targetWords').value) || 12000;
                const abstract = document.getElementById('paperAbstract').value.trim() || '';
                const keywords = document.getElementById('keywords').value.trim() || '';
                
                const paperData = {
                    title: title,
                    field: field,
                    type: paperType,
                    target_words: targetWords,
                    abstract: abstract,
                    keywords: keywords,
                    content: content,
                    html_content: quill.root.innerHTML,
                    created_at: new Date().toISOString(),
                    status: 'completed'
                };
                
                const response = await fetch('/api/save-paper', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(paperData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        showToast('论文已保存到数据库！论文ID: ' + result.paper_id);
                        localStorage.setItem('currentPaperId', result.paper_id);
                    } else {
                        showToast('保存失败: ' + result.message, 'error');
                    }
                } else {
                    showToast('保存到数据库失败: HTTP ' + response.status, 'error');
                }
                
            } catch (error) {
                console.error('保存论文到数据库失败:', error);
                showToast('保存到数据库失败，但草稿已本地保存', 'error');
            }
        }

        // 显示我的论文列表
        async function showMyPapers() {
            try {
                showLoading(true);
                const response = await fetch('/api/papers');
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data.papers.length > 0) {
                        showPapersModal(result.data.papers);
                    } else {
                        showToast('暂无保存的论文，快去生成一篇吧！');
                    }
                } else {
                    showToast('获取论文列表失败', 'error');
                }
                
            } catch (error) {
                console.error('获取论文列表失败:', error);
                showToast('获取论文列表失败: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // 显示论文列表模态框
        function showPapersModal(papers) {
            let papersHtml = '';
            papers.forEach(paper => {
                const createdDate = new Date(paper.created_at).toLocaleString('zh-CN');
                const contentLength = paper.content_length || 0;
                papersHtml += `
                    <div class="paper-item">
                        <div class="paper-title">${paper.title}</div>
                        <div class="paper-meta">
                            <i class="fas fa-tag"></i> ${paper.field || '未指定领域'} | 
                            <i class="fas fa-file-alt"></i> ${paper.paper_type || '本科毕业论文'} | 
                            <i class="fas fa-text-width"></i> ${contentLength}字符<br>
                            <i class="fas fa-clock"></i> 创建时间: ${createdDate}
                        </div>
                        <div class="paper-actions">
                            <button onclick="loadPaperToEditor(${paper.id})" class="action-btn">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                        </div>
                    </div>
                `;
            });
            
            const modalHtml = `
                <div id="papersModal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <div class="modal-title">我的论文 (${papers.length}篇)</div>
                            <button class="modal-close" onclick="closePapersModal()">×</button>
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            ${papersHtml}
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // 关闭论文列表模态框
        function closePapersModal() {
            const modal = document.getElementById('papersModal');
            if (modal) {
                modal.remove();
            }
        }

        // 加载论文到编辑器
        async function loadPaperToEditor(paperId) {
            try {
                console.log('加载论文到编辑器，ID:', paperId);
                showLoading(true);
                closePapersModal();
                
                const response = await fetch(`/api/paper/${paperId}`);
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.paper) {
                        const paper = result.paper;
                        
                        // 填充表单字段
                        document.getElementById('paperTitle').value = paper.title || '';
                        document.getElementById('researchField').value = paper.field || '';
                        document.getElementById('paperType').value = paper.paper_type || '本科毕业论文';
                        document.getElementById('targetWords').value = paper.target_words || 12000;
                        document.getElementById('paperAbstract').value = paper.abstract || '';
                        document.getElementById('keywords').value = paper.keywords || '';
                        
                        // 加载内容到编辑器
                        if (paper.content && typeof paper.content === 'object') {
                            quill.setContents(paper.content);
                        } else if (paper.html_content) {
                            try {
                                quill.clipboard.dangerouslyPasteHTML(paper.html_content);
                            } catch (error) {
                                const cleanText = paper.html_content.replace(/<[^>]*>/g, '');
                                quill.setText(cleanText);
                            }
                        }
                        
                        localStorage.setItem('currentPaperId', paperId);
                        showToast(`论文"${paper.title}"加载成功！`);
                        
                    } else {
                        showToast('论文不存在或加载失败', 'error');
                    }
                } else {
                    showToast('加载论文失败: HTTP ' + response.status, 'error');
                }
                
            } catch (error) {
                console.error('加载论文失败:', error);
                showToast('加载论文失败: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // 导出Word文档
        async function exportToWord() {
            try {
                const content = quill.getContents();
                const title = document.getElementById('paperTitle').value || '未命名论文';
                
                if (!content.ops || content.ops.length === 0) {
                    showToast('请先生成论文内容', 'error');
                    return;
                }

                showLoading(true);

                const response = await fetch('/api/export-paper-word', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        content: content,
                        html_content: quill.root.innerHTML
                    })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${title}_${new Date().toISOString().slice(0,10)}.docx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    showToast('Word文档导出成功！');
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.error || '导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                showToast('导出功能暂时不可用，您可以复制内容到Word中', 'error');
            } finally {
                showLoading(false);
            }
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = show ? 'flex' : 'none';
            }
        }

        // 显示提示信息
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            
            const icon = type === 'error' ? 'times-circle' : 'check-circle';
            toast.innerHTML = `
                <i class="fas fa-${icon}"></i>
                <span>${message}</span>
            `;
            
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => toast.classList.add('show'), 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
        // 初始化增强功能
        function initializeEnhancedFeatures() {
            // 字体选择
            document.getElementById('fontFamily').addEventListener('change', function() {
                const font = this.value;
                quill.format('font', font);
            });

            // 字号调整
            document.getElementById('fontSize').addEventListener('change', function() {
                const size = this.value + 'px';
                quill.format('size', size);
            });

            // 行距调整
            document.getElementById('lineHeight').addEventListener('change', function() {
                const lineHeight = this.value;
                const editor = document.querySelector('.ql-editor');
                editor.style.lineHeight = lineHeight;
            });

            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl+S 保存
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    saveDraft();
                }

                // F11 全屏
                if (e.key === 'F11') {
                    e.preventDefault();
                    toggleFullscreen();
                }

                // Ctrl+F 查找
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    findReplace();
                }
            });
        }

        // 插入表格
        function insertTable() {
            const rows = prompt('请输入行数:', '3');
            const cols = prompt('请输入列数:', '3');

            if (rows && cols) {
                let tableHTML = '<table style="border-collapse: collapse; width: 100%; margin: 20px 0;"><tbody>';
                for (let i = 0; i < parseInt(rows); i++) {
                    tableHTML += '<tr>';
                    for (let j = 0; j < parseInt(cols); j++) {
                        tableHTML += '<td style="border: 1px solid #ddd; padding: 8px;">单元格</td>';
                    }
                    tableHTML += '</tr>';
                }
                tableHTML += '</tbody></table>';

                const range = quill.getSelection();
                if (range) {
                    quill.clipboard.dangerouslyPasteHTML(range.index, tableHTML);
                    showToast('表格已插入', 'success');
                } else {
                    showToast('请先选择插入位置', 'warning');
                }
            }
        }

        // 插入数学公式
        function insertMath() {
            const formula = prompt('请输入LaTeX公式:', 'E = mc^2');
            if (formula) {
                const range = quill.getSelection();
                if (range) {
                    quill.insertText(range.index, `$$${formula}$$`);
                    showToast('数学公式已插入', 'success');
                } else {
                    showToast('请先选择插入位置', 'warning');
                }
            }
        }

        // 插入代码块
        function insertCodeBlock() {
            const code = prompt('请输入代码:', 'console.log("Hello World");');
            if (code) {
                const range = quill.getSelection();
                if (range) {
                    quill.insertText(range.index, '\n');
                    quill.formatLine(range.index + 1, 1, 'code-block', true);
                    quill.insertText(range.index + 1, code);
                    showToast('代码块已插入', 'success');
                } else {
                    showToast('请先选择插入位置', 'warning');
                }
            }
        }

        // 插入引用
        function insertQuote() {
            const quote = prompt('请输入引用内容:', '这是一段引用文字');
            if (quote) {
                const range = quill.getSelection();
                if (range) {
                    quill.insertText(range.index, '\n');
                    quill.formatLine(range.index + 1, 1, 'blockquote', true);
                    quill.insertText(range.index + 1, quote);
                    showToast('引用已插入', 'success');
                } else {
                    showToast('请先选择插入位置', 'warning');
                }
            }
        }

        // 插入脚注
        function insertFootnote() {
            const footnote = prompt('请输入脚注内容:', '这是脚注说明');
            if (footnote) {
                const range = quill.getSelection();
                if (range) {
                    const footnoteNumber = Math.floor(Math.random() * 100) + 1;
                    quill.insertText(range.index, `[${footnoteNumber}]`, 'script', 'super');
                    quill.insertText(range.index + 4, `\n\n[${footnoteNumber}] ${footnote}\n`);
                    showToast('脚注已插入', 'success');
                } else {
                    showToast('请先选择插入位置', 'warning');
                }
            }
        }

        // 查找替换
        function findReplace() {
            const searchText = prompt('查找内容:');
            if (searchText) {
                const replaceText = prompt('替换为:');
                if (replaceText !== null) {
                    const content = quill.getText();
                    const newContent = content.replace(new RegExp(searchText, 'g'), replaceText);
                    quill.setText(newContent);
                    showToast('替换完成', 'success');
                }
            }
        }

        // 切换全屏
        let isFullscreen = false;
        function toggleFullscreen() {
            const editorSection = document.querySelector('.editor-section');

            if (!isFullscreen) {
                editorSection.style.position = 'fixed';
                editorSection.style.top = '0';
                editorSection.style.left = '0';
                editorSection.style.width = '100vw';
                editorSection.style.height = '100vh';
                editorSection.style.zIndex = '9999';
                editorSection.style.background = 'white';

                // 调整编辑器高度
                const editor = document.querySelector('.editor-container');
                editor.style.height = 'calc(100vh - 200px)';

                isFullscreen = true;
                showToast('已进入全屏模式，按F11或点击按钮退出', 'info');
            } else {
                editorSection.style.position = '';
                editorSection.style.top = '';
                editorSection.style.left = '';
                editorSection.style.width = '';
                editorSection.style.height = '';
                editorSection.style.zIndex = '';
                editorSection.style.background = '';

                // 恢复编辑器高度
                const editor = document.querySelector('.editor-container');
                editor.style.height = '650px';

                isFullscreen = false;
                showToast('已退出全屏模式', 'info');
            }
        }

        // 显示Toast通知
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : type === 'error' ? 'danger' : 'info'} position-fixed`;
            toast.style.top = '20px';
            toast.style.right = '20px';
            toast.style.zIndex = '10000';
            toast.style.minWidth = '300px';
            toast.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }

    </script>
</body>
</html>