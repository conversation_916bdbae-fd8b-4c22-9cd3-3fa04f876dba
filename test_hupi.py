"""
测试虎皮椒支付功能
"""
import sys
import os
sys.path.append('/mnt/e/引流网站/sql4/hupijiao-v3-python')

try:
    # 直接导入模块
    import importlib.util
    spec = importlib.util.spec_from_file_location("hupijiao_module", 
                                                  '/mnt/e/引流网站/sql4/hupijiao-v3-python/hupijiao-v3-python.py')
    hupijiao_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(hupijiao_module)
    
    # 获取Hupi类
    Hupi = hupijiao_module.Hupi
    
    # 测试支付功能
    hupi = Hupi()
    print("虎皮椒支付实例创建成功")
    print(f"AppID: {hupi.appid}")
    print(f"支付接口: {hupi.api_urls}")
    
    # 测试订单参数生成
    test_data = {
        "version": "1.1",
        "appid": hupi.appid,
        "trade_order_id": "test_123456",
        "total_fee": "0.01",
        "title": "测试订单",
        "time": "1627890123",
        "notify_url": hupi.notify_url,
        "return_url": hupi.return_url,
        "callback_url": hupi.callback_url,
        "nonce_str": "test_nonce",
        "plugins": "flask_app"
    }
    
    # 测试签名生成
    signature = hupi.sign(test_data)
    print(f"测试签名: {signature}")
    
    print("虎皮椒支付模块测试通过！")
    
except Exception as e:
    print(f"虎皮椒支付模块测试失败: {e}")
    import traceback
    traceback.print_exc()