<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线工具集 - 专业文档与图表生成平台</title>
    
    <!-- 引入CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica', sans-serif;
            background: #f8fafb;
            min-height: 100vh;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* 头部样式 */
        .header {
            text-align: center;
            margin-bottom: 5rem;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 3.2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            letter-spacing: -1px;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header .subtitle {
            font-size: 1.3rem;
            opacity: 0.95;
            font-weight: 400;
            max-width: 650px;
            margin: 0 auto;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        /* 工具卡片网格 */
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        /* 工具卡片样式 */
        .tool-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            text-decoration: none;
            color: inherit;
            border: 1px solid #f1f5f9;
            transition: all 0.4s ease;
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .tool-card:hover::before {
            transform: scaleX(1);
        }

        .tool-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
            border-color: #e2e8f0;
        }

        .tool-icon {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 28px;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .sql-tool .tool-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }

        .paper-tool .tool-icon {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
        }

        .test-tool .tool-icon {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .defense-tool .tool-icon {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .paper-gen-tool .tool-icon {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .format-tool .tool-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .enhanced-editor-tool .tool-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }




        .tool-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
            color: #1f2937;
        }

        .tool-description {
            font-size: 14px;
            line-height: 1.5;
            color: #6b7280;
            text-align: center;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .tool-features {
            list-style: none;
            margin: 0 0 24px 0;
            padding: 0;
            display: grid;
            gap: 8px;
        }

        .tool-features li {
            padding: 8px 12px;
            color: #374151;
            background: #f8fafc;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            border-left: 3px solid #10b981;
            transition: all 0.2s ease;
            position: relative;
            padding-left: 32px;
        }

        .tool-features li::before {
            content: '✓';
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #10b981;
            font-weight: 600;
            font-size: 12px;
        }

        .tool-features li:hover {
            background: #f1f5f9;
            border-left-color: #059669;
            transform: translateX(2px);
        }

        .tool-status {
            text-align: center;
            margin-top: auto;
        }

        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid;
        }

        .status-available {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
            transition: all 0.3s ease;
        }

        .status-available:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }

        .status-ready {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: none;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
            transition: all 0.3s ease;
        }

        .status-ready:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
        }

        .status-coming-soon {
            background: #fef3c7;
            color: #d97706;
            border-color: #fed7aa;
        }

        /* 特性介绍区域 */
        .features-section {
            background: white;
            border-radius: 12px;
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            border: 1px solid #e8ecf0;
        }

        .features-title {
            text-align: center;
            color: #1e293b;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 2.5rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            text-align: center;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            background: #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.2rem;
            color: #3b82f6;
        }

        .feature-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }

        .feature-desc {
            color: #64748b;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        /* 页脚 */
        .footer {
            text-align: center;
            color: #94a3b8;
            padding: 2rem 0;
            border-top: 1px solid #e8ecf0;
            font-size: 0.9rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .tool-card {
                padding: 2rem;
            }
            
            .features-section {
                padding: 2rem 1rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .tool-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .tool-card:nth-child(2) {
            animation-delay: 0.1s;
        }

        /* 导航栏样式 */
        .navbar {
            background: white;
            border-bottom: 1px solid #e8ecf0;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.4rem;
            font-weight: 600;
            color: #1e293b;
            text-decoration: none;
        }

        .logo:hover {
            text-decoration: none;
            color: #3b82f6;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }

        .nav-link {
            color: #64748b;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: #3b82f6;
            text-decoration: none;
        }

        /* 用户信息样式 */
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-balance {
            background: #f0f9ff;
            color: #0369a1;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .user-dropdown {
            position: relative;
        }

        .user-trigger {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: #f8fafc;
            border-radius: 6px;
            color: #475569;
            text-decoration: none;
            transition: background-color 0.3s ease;
            cursor: pointer;
        }

        .user-trigger:hover {
            background: #e2e8f0;
            text-decoration: none;
            color: #475569;
        }

        .user-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            min-width: 160px;
            display: none;
            z-index: 1000;
        }

        .user-menu.show {
            display: block;
        }

        .user-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #475569;
            text-decoration: none;
            border-bottom: 1px solid #f1f5f9;
            transition: background-color 0.3s ease;
        }

        .user-menu a:last-child {
            border-bottom: none;
        }

        .user-menu a:hover {
            background: #f8fafc;
            color: #2563eb;
        }

        .logout-btn {
            color: #dc2626 !important;
        }

        .logout-btn:hover {
            background: #fef2f2 !important;
            color: #dc2626 !important;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-tools"></i> 在线工具集
            </a>
            <div class="nav-links">
                <a href="#features" class="nav-link">功能特色</a>
                <a href="#tools" class="nav-link">工具中心</a>

                {% if user_info %}
                <!-- 已登录用户 -->
                <div class="user-info">
                    <div class="user-balance">
                        余额: ¥{{ "%.2f"|format(user_info.balance) }}
                    </div>
                    <div class="user-dropdown">
                        <div class="user-trigger" onclick="toggleUserMenu()">
                            <i class="fas fa-user"></i>
                            <span>{{ user_info.username }}</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="user-menu" id="userMenu">
                            <a href="/profile">
                                <i class="fas fa-user-circle"></i> 个人中心
                            </a>
                            <a href="#" onclick="logout()" class="logout-btn">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </div>
                </div>
                {% else %}
                <!-- 未登录用户 -->
                <a href="/login" class="nav-link">
                    <i class="fas fa-sign-in-alt"></i> 登录
                </a>
                <a href="/register" class="nav-link">
                    <i class="fas fa-user-plus"></i> 注册
                </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>智能文档处理平台</h1>
            <p class="subtitle">基于AI技术的专业文档生成与图表制作解决方案，助力企业数字化转型</p>
        </header>

        <!-- 工具卡片 -->
        <div class="tools-grid" id="tools">
            <!-- SQL转ER图工具 -->
            <a href="/sql-to-er" class="tool-card sql-tool">
                <div class="tool-icon">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <h2 class="tool-title">SQL转ER图编辑器</h2>
                <p class="tool-description">
                    自动解析SQL建表语句，生成专业的实体关系图，支持可视化编辑和多格式导出
                </p>
                <ul class="tool-features">
                    <li>智能SQL语句解析</li>
                    <li>可视化拖拽编辑</li>
                    <li>支持多种数据库方言</li>
                    <li>导出PNG/SVG格式图片</li>
                    <li>生成Word/HTML文档</li>
                </ul>
                <div class="tool-status">
                    <span class="status-badge status-available">
                        <i class="fas fa-check-circle"></i> 立即使用
                    </span>
                </div>
            </a>

            <!-- 论文结构图生成器 -->
            <a href="/paper-structure" class="tool-card paper-tool">
                <div class="tool-icon">
                    <i class="fas fa-sitemap"></i>
                </div>
                <h2 class="tool-title">论文结构图生成器</h2>
                <p class="tool-description">
                    智能分析论文内容，自动生成清晰的结构图和思维导图，优化学术写作流程
                </p>
                <ul class="tool-features">
                    <li>智能文本结构分析</li>
                    <li>自动章节层次提取</li>
                    <li>多种图表样式模板</li>
                    <li>分层次可视化展示</li>
                    <li>高清图片批量导出</li>
                </ul>
                <div class="tool-status">
                    <span class="status-badge status-available">
                        <i class="fas fa-check-circle"></i> 立即使用
                    </span>
                </div>
            </a>

            <!-- AI测试用例生成器 -->
            <a href="/test-case-generator" class="tool-card test-tool">
                <div class="tool-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h2 class="tool-title">AI测试用例生成器</h2>
                <p class="tool-description">
                    基于AI技术，根据系统描述自动生成标准化测试用例表格，支持导出Word三线表格式
                </p>
                <ul class="tool-features">
                    <li>AI智能分析系统需求</li>
                    <li>自动生成测试用例</li>
                    <li>标准化表格格式</li>
                    <li>支持多种测试类型</li>
                    <li>导出Word三线表</li>
                </ul>
                <div class="tool-status">
                    <span class="status-badge status-available">
                        <i class="fas fa-check-circle"></i> 立即使用
                    </span>
                </div>
            </a>

            <!-- 论文答辩问题生成器 -->
            <a href="/thesis-defense" class="tool-card defense-tool">
                <div class="tool-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h2 class="tool-title">论文答辩问题生成器</h2>
                <p class="tool-description">
                    基于AI技术，根据论文内容和系统描述，智能生成答辩过程中可能遇到的问题及标准答案
                </p>
                <ul class="tool-features">
                    <li>智能分析论文内容</li>
                    <li>模拟真实答辩场景</li>
                    <li>生成标准参考答案</li>
                    <li>多维度问题覆盖</li>
                    <li>导出答辩准备材料</li>
                </ul>
                <div class="tool-status">
                    <span class="status-badge status-ready">
                        <i class="fas fa-check-circle"></i> 立即使用
                    </span>
                </div>
            </a>



            <!-- 增强富文本编辑器 -->
            <a href="/enhanced-editor" class="tool-card enhanced-editor-tool">
                <div class="tool-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <h2 class="tool-title">增强富文本编辑器</h2>
                <p class="tool-description">
                    功能强大的专业文档编辑工具，支持丰富的格式和高级功能
                </p>
                <ul class="tool-features">
                    <li>丰富的文本格式</li>
                    <li>表格和图片插入</li>
                    <li>数学公式编辑</li>
                    <li>代码语法高亮</li>
                    <li>文档大纲导航</li>
                    <li>实时字数统计</li>
                    <li>自动保存功能</li>
                </ul>
                <div class="tool-status">
                    <span class="status-badge status-ready">
                        <i class="fas fa-check-circle"></i> 立即使用
                    </span>
                </div>
            </a>

            <!-- AI论文生成器 -->
            <a href="/paper-generator" class="tool-card paper-gen-tool">
                <div class="tool-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h2 class="tool-title">AI论文生成器</h2>
                <p class="tool-description">
                    基于人工智能技术的学术论文写作助手，让学术写作更简单高效
                </p>
                <ul class="tool-features">
                    <li>智能分析论文要求</li>
                    <li>自动生成论文内容</li>
                    <li>支持多种论文类型</li>
                    <li>富文本编辑功能</li>
                    <li>导出Word文档</li>
                </ul>
                <div class="tool-status">
                    <span class="status-badge status-ready">
                        <i class="fas fa-check-circle"></i> 立即使用
                    </span>
                </div>
            </a>


        </div>
    </div>

        <!-- 平台特性 -->
        <section class="features-section" id="features">
            <h2 class="features-title">平台优势</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title">高效便捷</h3>
                    <p class="feature-desc">无需安装软件，浏览器直接使用，快速处理文档和图表需求</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">安全可靠</h3>
                    <p class="feature-desc">数据本地处理，保护用户隐私，企业级安全标准</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3 class="feature-title">多格式导出</h3>
                    <p class="feature-desc">支持PNG、SVG、PDF、Word等多种格式，满足不同使用场景</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">跨平台支持</h3>
                    <p class="feature-desc">响应式设计，完美适配桌面、平板、手机等各种设备</p>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="footer">
            <p>&copy; 2024 在线工具集. 专注于提供高效的文档与图表生成解决方案</p>
        </footer>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease';
            
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 用户菜单切换
        function toggleUserMenu() {
            const menu = document.getElementById('userMenu');
            if (menu) {
                menu.classList.toggle('show');
            }
        }

        // 点击其他地方关闭菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.querySelector('.user-dropdown');
            const userMenu = document.getElementById('userMenu');

            if (userDropdown && userMenu && !userDropdown.contains(event.target)) {
                userMenu.classList.remove('show');
            }
        });

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('退出登录失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('退出登录失败');
                });
            }
        }
    </script>
</body>
</html> 