"""
测试支付成功页面访问
"""
import requests

try:
    # 测试支付成功页面
    response = requests.get('http://localhost:5000/payment/success/')
    print(f"访问 /payment/success/ - 状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ 支付成功页面正常访问")
    else:
        print(f"❌ 支付成功页面访问失败: {response.status_code}")
        
    # 测试不带斜杠的版本
    response2 = requests.get('http://localhost:5000/payment/success')
    print(f"访问 /payment/success - 状态码: {response2.status_code}")
    
    if response2.status_code == 200:
        print("✅ 支付成功页面（不带斜杠）正常访问")
    else:
        print(f"❌ 支付成功页面（不带斜杠）访问失败: {response2.status_code}")
        
except requests.exceptions.ConnectionError:
    print("⚠️  无法连接到Flask应用，请确保应用正在运行")
    print("可以运行：./start_payment_test.sh 启动应用")
except Exception as e:
    print(f"测试失败: {e}")